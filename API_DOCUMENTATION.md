# DLL Tracker API 文档

## 概述

DLL Tracker 是一个用于安卓应用异常日志上报和下载记录追踪的后端服务。

## 基础信息

- **基础URL**: `http://localhost:9900`
- **Content-Type**: `application/json`
- **响应格式**: JSON

## 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

## 异常日志 API

### 1. 上报异常日志

**接口**: `POST /api/exception-logs/report`

**请求体**:
```json
{
  "appPackage": "com.example.app",
  "appVersion": "1.0.0",
  "deviceId": "device123",
  "deviceModel": "Samsung Galaxy S21",
  "osVersion": "Android 12",
  "exceptionType": "NullPointerException",
  "exceptionMessage": "Attempt to invoke virtual method",
  "stackTrace": "java.lang.NullPointerException...",
  "exceptionTime": "2023-12-31T23:59:59",
  "userId": "user123",
  "pageName": "MainActivity",
  "networkStatus": "WiFi",
  "memoryUsage": "512MB/2GB",
  "batteryLevel": 85,
  "extraInfo": "{\"key\":\"value\"}"
}
```

### 2. 查询异常日志

**接口**: `GET /api/exception-logs`

**查询参数**:
- `page`: 页码（默认0）
- `size`: 每页大小（默认20）
- `sortBy`: 排序字段（默认exceptionTime）
- `sortDir`: 排序方向（asc/desc，默认desc）

### 3. 根据应用包名查询

**接口**: `GET /api/exception-logs/by-app`

**查询参数**:
- `appPackage`: 应用包名（必填）
- `appVersion`: 应用版本（可选）
- `page`: 页码
- `size`: 每页大小

### 4. 根据设备ID查询

**接口**: `GET /api/exception-logs/by-device`

**查询参数**:
- `deviceId`: 设备ID（必填）
- `page`: 页码
- `size`: 每页大小

### 5. 根据异常类型查询

**接口**: `GET /api/exception-logs/by-type`

**查询参数**:
- `exceptionType`: 异常类型（必填）
- `page`: 页码
- `size`: 每页大小

### 6. 根据时间范围查询

**接口**: `GET /api/exception-logs/by-time-range`

**查询参数**:
- `startTime`: 开始时间（ISO格式，必填）
- `endTime`: 结束时间（ISO格式，必填）
- `appPackage`: 应用包名（可选）
- `page`: 页码
- `size`: 每页大小

### 7. 获取最近异常日志

**接口**: `GET /api/exception-logs/recent`

### 8. 统计异常日志数量

**接口**: `GET /api/exception-logs/count`

**查询参数**:
- `appPackage`: 应用包名（可选）
- `startTime`: 开始时间（可选）
- `endTime`: 结束时间（可选）

## 下载记录 API

### 1. 上报下载记录

**接口**: `POST /api/download-records/report`

**请求体**:
```json
{
  "appPackage": "com.example.app",
  "appVersion": "1.0.0",
  "deviceId": "device123",
  "deviceModel": "Samsung Galaxy S21",
  "osVersion": "Android 12",
  "fileName": "update.apk",
  "fileUrl": "https://example.com/update.apk",
  "fileSize": 1024000,
  "fileType": "APK",
  "downloadStatus": 1,
  "downloadStartTime": "2023-12-31T23:59:59",
  "downloadEndTime": "2023-12-31T23:59:59",
  "downloadDuration": 30000,
  "downloadSpeed": 68.27,
  "userId": "user123",
  "sourcePage": "UpdateActivity",
  "networkType": "WiFi",
  "failureReason": null,
  "localPath": "/storage/downloads/update.apk",
  "extraInfo": "{\"key\":\"value\"}"
}
```

**下载状态说明**:
- 0: 开始下载
- 1: 下载成功
- 2: 下载失败
- 3: 下载取消

### 2. 查询下载记录

**接口**: `GET /api/download-records`

**查询参数**:
- `page`: 页码（默认0）
- `size`: 每页大小（默认20）
- `sortBy`: 排序字段（默认downloadStartTime）
- `sortDir`: 排序方向（asc/desc，默认desc）

### 3. 根据应用包名查询

**接口**: `GET /api/download-records/by-app`

### 4. 根据设备ID查询

**接口**: `GET /api/download-records/by-device`

### 5. 根据下载状态查询

**接口**: `GET /api/download-records/by-status`

**查询参数**:
- `downloadStatus`: 下载状态（必填）

### 6. 根据文件类型查询

**接口**: `GET /api/download-records/by-file-type`

**查询参数**:
- `fileType`: 文件类型（必填）

### 7. 根据时间范围查询

**接口**: `GET /api/download-records/by-time-range`

### 8. 获取最近下载记录

**接口**: `GET /api/download-records/recent`

### 9. 统计下载记录数量

**接口**: `GET /api/download-records/count`

## 错误码说明

- 200: 成功
- 400: 请求参数错误
- 404: 资源不存在
- 500: 服务器内部错误

## 使用示例

### 上报异常日志示例

```bash
curl -X POST http://localhost:9900/api/exception-logs/report \
  -H "Content-Type: application/json" \
  -d '{
    "appPackage": "com.example.myapp",
    "appVersion": "1.0.0",
    "deviceId": "device123",
    "exceptionType": "NullPointerException",
    "exceptionMessage": "Test exception",
    "exceptionTime": "2023-12-31T23:59:59"
  }'
```

### 查询异常日志示例

```bash
curl "http://localhost:9900/api/exception-logs?page=0&size=10"
```
