server:
  port: 9900
spring:
  application:
    name: dll-tracker
  profiles:
    active: prod
  main:
    allow-circular-references: true
  # 数据库配置
  datasource:
    url: ********************************************************************************************************************************************************************************************************* # MySQL Connector/J 8.X 连接的示例
    username: dll_root
    password: Dll@20220317
    driver-class-name: com.mysql.cj.jdbc.Driver
    # HikariCP 配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  # JPA配置
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
    show-sql: true
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        format_sql: true