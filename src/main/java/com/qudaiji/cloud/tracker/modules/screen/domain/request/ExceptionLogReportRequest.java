package com.qudaiji.cloud.tracker.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 异常日志数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExceptionLogDTO {
    
    /**
     * 应用包名
     */
    @NotBlank(message = "应用包名不能为空")
    private String appPackage;
    
    /**
     * 应用版本号
     */
    private String appVersion;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备型号
     */
    private String deviceModel;
    
    /**
     * 操作系统版本
     */
    private String osVersion;
    
    /**
     * 异常类型
     */
    @NotBlank(message = "异常类型不能为空")
    private String exceptionType;
    
    /**
     * 异常消息
     */
    private String exceptionMessage;
    
    /**
     * 异常堆栈信息
     */
    private String stackTrace;
    
    /**
     * 异常发生时间
     */
    @NotNull(message = "异常发生时间不能为空")
    private LocalDateTime exceptionTime;
    
    /**
     * 用户ID（可选）
     */
    private String userId;
    
    /**
     * 异常发生的页面/活动
     */
    private String pageName;
    
    /**
     * 网络状态
     */
    private String networkStatus;
    
    /**
     * 内存使用情况
     */
    private String memoryUsage;
    
    /**
     * 电池电量
     */
    private Integer batteryLevel;
    
    /**
     * 额外信息（JSON格式）
     */
    private String extraInfo;
}
