package com.qudaiji.cloud.tracker.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 下载记录数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DownloadRecordDTO {
    
    /**
     * 应用包名
     */
    @NotBlank(message = "应用包名不能为空")
    private String appPackage;
    
    /**
     * 应用版本号
     */
    private String appVersion;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备型号
     */
    private String deviceModel;
    
    /**
     * 操作系统版本
     */
    private String osVersion;
    
    /**
     * 下载文件名
     */
    @NotBlank(message = "下载文件名不能为空")
    private String fileName;
    
    /**
     * 下载文件URL
     */
    @NotBlank(message = "下载文件URL不能为空")
    private String fileUrl;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件类型
     */
    private String fileType;
    
    /**
     * 下载状态：0-开始下载，1-下载成功，2-下载失败，3-下载取消
     */
    @NotNull(message = "下载状态不能为空")
    private Integer downloadStatus;
    
    /**
     * 下载开始时间
     */
    @NotNull(message = "下载开始时间不能为空")
    private LocalDateTime downloadStartTime;
    
    /**
     * 下载结束时间
     */
    private LocalDateTime downloadEndTime;
    
    /**
     * 下载耗时（毫秒）
     */
    private Long downloadDuration;
    
    /**
     * 下载速度（KB/s）
     */
    private Double downloadSpeed;
    
    /**
     * 用户ID（可选）
     */
    private String userId;
    
    /**
     * 下载来源页面
     */
    private String sourcePage;
    
    /**
     * 网络类型
     */
    private String networkType;
    
    /**
     * 失败原因（下载失败时）
     */
    private String failureReason;
    
    /**
     * 本地存储路径
     */
    private String localPath;
    
    /**
     * 额外信息（JSON格式）
     */
    private String extraInfo;
}
