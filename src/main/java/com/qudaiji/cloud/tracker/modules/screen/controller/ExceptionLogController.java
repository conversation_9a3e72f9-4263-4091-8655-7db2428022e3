package com.qudaiji.cloud.tracker.modules.screen.controller;

import com.qudaiji.cloud.tracker.modules.screen.domain.request.ExceptionLogReportRequest;
import com.qudaiji.cloud.tracker.common.response.ApiResponse;
import com.qudaiji.cloud.tracker.modules.screen.entity.ExceptionLog;
import com.qudaiji.cloud.tracker.modules.screen.service.ExceptionLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 异常日志控制器
 */
@RestController
@RequestMapping("/api/exception")
@RequiredArgsConstructor
@Slf4j
@Validated
public class ExceptionLogController {

    private final ExceptionLogService exceptionLogService;

    /**
     * 上报异常日志
     */
    @PostMapping("/report")
    public ApiResponse<ExceptionLog> reportException(@RequestBody ExceptionLogReportRequest exceptionLogDTO) {
        try {
            log.info("接收异常日志上报请求: appPackage={}, exceptionType={}",
                    exceptionLogDTO.getAppPackage(), exceptionLogDTO.getExceptionType());

            ExceptionLog savedLog = exceptionLogService.saveExceptionLog(exceptionLogDTO);
            return ApiResponse.success("异常日志上报成功", savedLog);
        } catch (Exception e) {
            log.error("异常日志上报失败", e);
            return ApiResponse.error("异常日志上报失败: " + e.getMessage());
        }
    }
}
