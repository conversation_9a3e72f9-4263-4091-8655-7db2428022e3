package com.qudaiji.cloud.tracker.modules.screen.service;

import com.qudaiji.cloud.tracker.modules.screen.domain.request.ExceptionLogReportRequest;
import com.qudaiji.cloud.tracker.modules.screen.entity.ExceptionLog;
import com.qudaiji.cloud.tracker.modules.screen.repository.ExceptionLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 异常日志业务逻辑层
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExceptionLogService {

    private final ExceptionLogRepository exceptionLogRepository;

    /**
     * 保存异常日志
     */
    @Transactional
    public ExceptionLog saveExceptionLog(ExceptionLogReportRequest exceptionLogDTO) {
        log.info("保存异常日志: appPackage={}, exceptionType={}",
                exceptionLogDTO.getAppPackage(), exceptionLogDTO.getExceptionType());

        ExceptionLog exceptionLog = new ExceptionLog();
        BeanUtils.copyProperties(exceptionLogDTO, exceptionLog);

        ExceptionLog savedLog = exceptionLogRepository.save(exceptionLog);
        log.info("异常日志保存成功, ID={}", savedLog.getId());

        return savedLog;
    }
}
