package com.qudaiji.cloud.tracker.modules.screen.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 异常日志实体
 */
@Entity
@Table(name = "exception_log")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExceptionLog {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 应用包名
     */
    @Column(name = "environment", nullable = false, length = 255)
    private String environment;

    /**
     * 应用版本号
     */
    @Column(name = "app_version", length = 50)
    private String appVersion;

    /**
     * 设备编码
     */
    @Column(name = "device_code", length = 255)
    private String deviceCode;

    /**
     * 屏幕编码
     */
    @Column(name = "screen_code", length = 100)
    private String screenCode;

    /**
     * 异常类型
     */
    @Column(name = "log_level", nullable = false, length = 255)
    private String exceptionType;

    /**
     * 异常类型
     */
    @Column(name = "device_status", nullable = false, length = 255)
    private String deviceStatus;

    /**
     * 异常消息
     */
    @Column(name = "exception_message", columnDefinition = "TEXT")
    private String exceptionMessage;

    /**
     * 异常堆栈信息
     */
    @Column(name = "stack_trace", columnDefinition = "LONGTEXT")
    private String stackTrace;

    /**
     * 额外信息（JSON格式）
     */
    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_time", nullable = false, updatable = false)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_time", nullable = false)
    private LocalDateTime updatedTime;
}
