package com.qudaiji.cloud.tracker.modules.screen.controller;

import com.qudaiji.cloud.tracker.common.dto.DownloadRecordDTO;
import com.qudaiji.cloud.tracker.common.response.ApiResponse;
import com.qudaiji.cloud.tracker.modules.screen.entity.DownloadRecord;
import com.qudaiji.cloud.tracker.modules.screen.service.DownloadRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 下载记录控制器
 */
@RestController
@RequestMapping("/api/download-records")
@RequiredArgsConstructor
@Slf4j
@Validated
public class DownloadRecordController {
    
    private final DownloadRecordService downloadRecordService;
    
    /**
     * 上报下载记录
     */
    @PostMapping("/report")
    public ApiResponse<DownloadRecord> reportDownload(@Valid @RequestBody DownloadRecordDTO downloadRecordDTO) {
        try {
            log.info("接收下载记录上报请求: appPackage={}, fileName={}, status={}", 
                    downloadRecordDTO.getAppPackage(), downloadRecordDTO.getFileName(), downloadRecordDTO.getDownloadStatus());
            
            DownloadRecord savedRecord = downloadRecordService.saveDownloadRecord(downloadRecordDTO);
            return ApiResponse.success("下载记录上报成功", savedRecord);
        } catch (Exception e) {
            log.error("下载记录上报失败", e);
            return ApiResponse.error("下载记录上报失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询下载记录
     */
    @GetMapping("/{id}")
    public ApiResponse<DownloadRecord> getDownloadRecordById(@PathVariable Long id) {
        try {
            Optional<DownloadRecord> downloadRecord = downloadRecordService.findById(id);
            if (downloadRecord.isPresent()) {
                return ApiResponse.success(downloadRecord.get());
            } else {
                return ApiResponse.error(404, "下载记录不存在");
            }
        } catch (Exception e) {
            log.error("查询下载记录失败", e);
            return ApiResponse.error("查询下载记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询下载记录
     */
    @GetMapping
    public ApiResponse<Page<DownloadRecord>> getDownloadRecords(
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "20") @Min(1) int size,
            @RequestParam(defaultValue = "downloadStartTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<DownloadRecord> downloadRecords = downloadRecordService.findAll(pageable);
            return ApiResponse.success(downloadRecords);
        } catch (Exception e) {
            log.error("分页查询下载记录失败", e);
            return ApiResponse.error("分页查询下载记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据应用包名查询下载记录
     */
    @GetMapping("/by-app")
    public ApiResponse<Page<DownloadRecord>> getDownloadRecordsByApp(
            @RequestParam String appPackage,
            @RequestParam(required = false) String appVersion,
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "20") @Min(1) int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("downloadStartTime").descending());
            
            Page<DownloadRecord> downloadRecords;
            if (appVersion != null && !appVersion.trim().isEmpty()) {
                downloadRecords = downloadRecordService.findByAppPackageAndVersion(appPackage, appVersion, pageable);
            } else {
                downloadRecords = downloadRecordService.findByAppPackage(appPackage, pageable);
            }
            
            return ApiResponse.success(downloadRecords);
        } catch (Exception e) {
            log.error("根据应用包名查询下载记录失败", e);
            return ApiResponse.error("根据应用包名查询下载记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据设备ID查询下载记录
     */
    @GetMapping("/by-device")
    public ApiResponse<Page<DownloadRecord>> getDownloadRecordsByDevice(
            @RequestParam String deviceId,
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "20") @Min(1) int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("downloadStartTime").descending());
            Page<DownloadRecord> downloadRecords = downloadRecordService.findByDeviceId(deviceId, pageable);
            return ApiResponse.success(downloadRecords);
        } catch (Exception e) {
            log.error("根据设备ID查询下载记录失败", e);
            return ApiResponse.error("根据设备ID查询下载记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据下载状态查询下载记录
     */
    @GetMapping("/by-status")
    public ApiResponse<Page<DownloadRecord>> getDownloadRecordsByStatus(
            @RequestParam Integer downloadStatus,
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "20") @Min(1) int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("downloadStartTime").descending());
            Page<DownloadRecord> downloadRecords = downloadRecordService.findByDownloadStatus(downloadStatus, pageable);
            return ApiResponse.success(downloadRecords);
        } catch (Exception e) {
            log.error("根据下载状态查询下载记录失败", e);
            return ApiResponse.error("根据下载状态查询下载记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件类型查询下载记录
     */
    @GetMapping("/by-file-type")
    public ApiResponse<Page<DownloadRecord>> getDownloadRecordsByFileType(
            @RequestParam String fileType,
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "20") @Min(1) int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("downloadStartTime").descending());
            Page<DownloadRecord> downloadRecords = downloadRecordService.findByFileType(fileType, pageable);
            return ApiResponse.success(downloadRecords);
        } catch (Exception e) {
            log.error("根据文件类型查询下载记录失败", e);
            return ApiResponse.error("根据文件类型查询下载记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据时间范围查询下载记录
     */
    @GetMapping("/by-time-range")
    public ApiResponse<Page<DownloadRecord>> getDownloadRecordsByTimeRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(required = false) String appPackage,
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "20") @Min(1) int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("downloadStartTime").descending());

            Page<DownloadRecord> downloadRecords;
            if (appPackage != null && !appPackage.trim().isEmpty()) {
                downloadRecords = downloadRecordService.findByAppPackageAndTimeRange(appPackage, startTime, endTime, pageable);
            } else {
                downloadRecords = downloadRecordService.findByTimeRange(startTime, endTime, pageable);
            }

            return ApiResponse.success(downloadRecords);
        } catch (Exception e) {
            log.error("根据时间范围查询下载记录失败", e);
            return ApiResponse.error("根据时间范围查询下载记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近的下载记录
     */
    @GetMapping("/recent")
    public ApiResponse<List<DownloadRecord>> getRecentDownloadRecords() {
        try {
            List<DownloadRecord> recentRecords = downloadRecordService.getRecentDownloadRecords();
            return ApiResponse.success(recentRecords);
        } catch (Exception e) {
            log.error("获取最近下载记录失败", e);
            return ApiResponse.error("获取最近下载记录失败: " + e.getMessage());
        }
    }

    /**
     * 统计下载记录数量
     */
    @GetMapping("/count")
    public ApiResponse<Long> countDownloadRecords(
            @RequestParam(required = false) String appPackage,
            @RequestParam(required = false) Integer downloadStatus,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        try {
            Long count;
            if (appPackage != null && !appPackage.trim().isEmpty()) {
                count = downloadRecordService.countByAppPackage(appPackage);
            } else if (downloadStatus != null) {
                count = downloadRecordService.countByDownloadStatus(downloadStatus);
            } else if (startTime != null && endTime != null) {
                count = downloadRecordService.countByTimeRange(startTime, endTime);
            } else {
                count = downloadRecordService.findAll(Pageable.unpaged()).getTotalElements();
            }

            return ApiResponse.success(count);
        } catch (Exception e) {
            log.error("统计下载记录数量失败", e);
            return ApiResponse.error("统计下载记录数量失败: " + e.getMessage());
        }
    }

    /**
     * 删除下载记录
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteDownloadRecord(@PathVariable Long id) {
        try {
            Optional<DownloadRecord> downloadRecord = downloadRecordService.findById(id);
            if (!downloadRecord.isPresent()) {
                return ApiResponse.error(404, "下载记录不存在");
            }

            downloadRecordService.deleteById(id);
            return ApiResponse.success("下载记录删除成功");
        } catch (Exception e) {
            log.error("删除下载记录失败", e);
            return ApiResponse.error("删除下载记录失败: " + e.getMessage());
        }
    }
}
