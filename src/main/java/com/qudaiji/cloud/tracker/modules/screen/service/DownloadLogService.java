package com.qudaiji.cloud.tracker.modules.screen.service;

import com.qudaiji.cloud.tracker.common.dto.DownloadRecordDTO;
import com.qudaiji.cloud.tracker.modules.screen.entity.DownloadRecord;
import com.qudaiji.cloud.tracker.modules.screen.repository.DownloadRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 下载记录业务逻辑层
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DownloadRecordService {
    
    private final DownloadRecordRepository downloadRecordRepository;
    
    /**
     * 保存下载记录
     */
    @Transactional
    public DownloadRecord saveDownloadRecord(DownloadRecordDTO downloadRecordDTO) {
        log.info("保存下载记录: appPackage={}, fileName={}, status={}", 
                downloadRecordDTO.getAppPackage(), downloadRecordDTO.getFileName(), downloadRecordDTO.getDownloadStatus());
        
        DownloadRecord downloadRecord = new DownloadRecord();
        BeanUtils.copyProperties(downloadRecordDTO, downloadRecord);
        
        DownloadRecord savedRecord = downloadRecordRepository.save(downloadRecord);
        log.info("下载记录保存成功, ID={}", savedRecord.getId());
        
        return savedRecord;
    }
    
    /**
     * 根据ID查询下载记录
     */
    public Optional<DownloadRecord> findById(Long id) {
        return downloadRecordRepository.findById(id);
    }
    
    /**
     * 分页查询所有下载记录
     */
    public Page<DownloadRecord> findAll(Pageable pageable) {
        return downloadRecordRepository.findAll(pageable);
    }
    
    /**
     * 根据应用包名分页查询下载记录
     */
    public Page<DownloadRecord> findByAppPackage(String appPackage, Pageable pageable) {
        return downloadRecordRepository.findByAppPackage(appPackage, pageable);
    }
    
    /**
     * 根据应用包名和版本分页查询下载记录
     */
    public Page<DownloadRecord> findByAppPackageAndVersion(String appPackage, String appVersion, Pageable pageable) {
        return downloadRecordRepository.findByAppPackageAndAppVersion(appPackage, appVersion, pageable);
    }
    
    /**
     * 根据设备ID分页查询下载记录
     */
    public Page<DownloadRecord> findByDeviceId(String deviceId, Pageable pageable) {
        return downloadRecordRepository.findByDeviceId(deviceId, pageable);
    }
    
    /**
     * 根据下载状态分页查询下载记录
     */
    public Page<DownloadRecord> findByDownloadStatus(Integer downloadStatus, Pageable pageable) {
        return downloadRecordRepository.findByDownloadStatus(downloadStatus, pageable);
    }
    
    /**
     * 根据时间范围分页查询下载记录
     */
    public Page<DownloadRecord> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        return downloadRecordRepository.findByDownloadStartTimeBetween(startTime, endTime, pageable);
    }
    
    /**
     * 根据应用包名和时间范围分页查询下载记录
     */
    public Page<DownloadRecord> findByAppPackageAndTimeRange(String appPackage, LocalDateTime startTime, 
                                                             LocalDateTime endTime, Pageable pageable) {
        return downloadRecordRepository.findByAppPackageAndDownloadStartTimeBetween(appPackage, startTime, endTime, pageable);
    }
    
    /**
     * 统计指定应用包名的下载数量
     */
    public Long countByAppPackage(String appPackage) {
        return downloadRecordRepository.countByAppPackage(appPackage);
    }
    
    /**
     * 统计指定时间范围内的下载数量
     */
    public Long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return downloadRecordRepository.countByDownloadStartTimeBetween(startTime, endTime);
    }
    
    /**
     * 统计指定状态的下载数量
     */
    public Long countByDownloadStatus(Integer status) {
        return downloadRecordRepository.countByDownloadStatus(status);
    }
    
    /**
     * 获取最近的下载记录
     */
    public List<DownloadRecord> getRecentDownloadRecords() {
        return downloadRecordRepository.findTop10ByOrderByDownloadStartTimeDesc();
    }
    
    /**
     * 根据用户ID分页查询下载记录
     */
    public Page<DownloadRecord> findByUserId(String userId, Pageable pageable) {
        return downloadRecordRepository.findByUserId(userId, pageable);
    }
    
    /**
     * 根据文件类型分页查询下载记录
     */
    public Page<DownloadRecord> findByFileType(String fileType, Pageable pageable) {
        return downloadRecordRepository.findByFileType(fileType, pageable);
    }
    
    /**
     * 删除下载记录
     */
    @Transactional
    public void deleteById(Long id) {
        log.info("删除下载记录: ID={}", id);
        downloadRecordRepository.deleteById(id);
    }
}
