package com.qudaiji.cloud.tracker.modules.screen.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 下载记录实体
 */
@Entity
@Table(name = "download_record")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DownloadRecord {
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 应用包名
     */
    @Column(name = "app_package", nullable = false, length = 255)
    private String appPackage;
    
    /**
     * 应用版本号
     */
    @Column(name = "app_version", length = 50)
    private String appVersion;
    
    /**
     * 设备ID
     */
    @Column(name = "device_id", length = 255)
    private String deviceId;
    
    /**
     * 设备型号
     */
    @Column(name = "device_model", length = 100)
    private String deviceModel;
    
    /**
     * 操作系统版本
     */
    @Column(name = "os_version", length = 50)
    private String osVersion;
    
    /**
     * 下载文件名
     */
    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;
    
    /**
     * 下载文件URL
     */
    @Column(name = "file_url", nullable = false, columnDefinition = "TEXT")
    private String fileUrl;
    
    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size")
    private Long fileSize;
    
    /**
     * 文件类型
     */
    @Column(name = "file_type", length = 100)
    private String fileType;
    
    /**
     * 下载状态：0-开始下载，1-下载成功，2-下载失败，3-下载取消
     */
    @Column(name = "download_status", nullable = false)
    private Integer downloadStatus;
    
    /**
     * 下载开始时间
     */
    @Column(name = "download_start_time", nullable = false)
    private LocalDateTime downloadStartTime;
    
    /**
     * 下载结束时间
     */
    @Column(name = "download_end_time")
    private LocalDateTime downloadEndTime;
    
    /**
     * 下载耗时（毫秒）
     */
    @Column(name = "download_duration")
    private Long downloadDuration;
    
    /**
     * 下载速度（KB/s）
     */
    @Column(name = "download_speed")
    private Double downloadSpeed;
    
    /**
     * 用户ID（可选）
     */
    @Column(name = "user_id", length = 100)
    private String userId;
    
    /**
     * 下载来源页面
     */
    @Column(name = "source_page", length = 255)
    private String sourcePage;
    
    /**
     * 网络类型
     */
    @Column(name = "network_type", length = 50)
    private String networkType;
    
    /**
     * 失败原因（下载失败时）
     */
    @Column(name = "failure_reason", columnDefinition = "TEXT")
    private String failureReason;
    
    /**
     * 本地存储路径
     */
    @Column(name = "local_path", columnDefinition = "TEXT")
    private String localPath;
    
    /**
     * 额外信息（JSON格式）
     */
    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;
    
    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}
