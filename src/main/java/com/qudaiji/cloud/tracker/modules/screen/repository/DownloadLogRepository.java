package com.qudaiji.cloud.tracker.modules.screen.repository;

import com.qudaiji.cloud.tracker.modules.screen.entity.DownloadRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 下载记录数据访问层
 */
@Repository
public interface DownloadRecordRepository extends JpaRepository<DownloadRecord, Long> {
    
    /**
     * 根据应用包名查询下载记录
     */
    Page<DownloadRecord> findByAppPackage(String appPackage, Pageable pageable);
    
    /**
     * 根据应用包名和版本查询下载记录
     */
    Page<DownloadRecord> findByAppPackageAndAppVersion(String appPackage, String appVersion, Pageable pageable);
    
    /**
     * 根据设备ID查询下载记录
     */
    Page<DownloadRecord> findByDeviceId(String deviceId, Pageable pageable);
    
    /**
     * 根据下载状态查询下载记录
     */
    Page<DownloadRecord> findByDownloadStatus(Integer downloadStatus, Pageable pageable);
    
    /**
     * 根据时间范围查询下载记录
     */
    @Query("SELECT d FROM DownloadRecord d WHERE d.downloadStartTime BETWEEN :startTime AND :endTime ORDER BY d.downloadStartTime DESC")
    Page<DownloadRecord> findByDownloadStartTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                                        @Param("endTime") LocalDateTime endTime, 
                                                        Pageable pageable);
    
    /**
     * 根据应用包名和时间范围查询下载记录
     */
    @Query("SELECT d FROM DownloadRecord d WHERE d.appPackage = :appPackage AND d.downloadStartTime BETWEEN :startTime AND :endTime ORDER BY d.downloadStartTime DESC")
    Page<DownloadRecord> findByAppPackageAndDownloadStartTimeBetween(@Param("appPackage") String appPackage,
                                                                     @Param("startTime") LocalDateTime startTime,
                                                                     @Param("endTime") LocalDateTime endTime,
                                                                     Pageable pageable);
    
    /**
     * 统计指定应用包名的下载数量
     */
    @Query("SELECT COUNT(d) FROM DownloadRecord d WHERE d.appPackage = :appPackage")
    Long countByAppPackage(@Param("appPackage") String appPackage);
    
    /**
     * 统计指定时间范围内的下载数量
     */
    @Query("SELECT COUNT(d) FROM DownloadRecord d WHERE d.downloadStartTime BETWEEN :startTime AND :endTime")
    Long countByDownloadStartTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定状态的下载数量
     */
    @Query("SELECT COUNT(d) FROM DownloadRecord d WHERE d.downloadStatus = :status")
    Long countByDownloadStatus(@Param("status") Integer status);
    
    /**
     * 获取最近的下载记录
     */
    List<DownloadRecord> findTop10ByOrderByDownloadStartTimeDesc();
    
    /**
     * 根据用户ID查询下载记录
     */
    Page<DownloadRecord> findByUserId(String userId, Pageable pageable);
    
    /**
     * 根据文件类型查询下载记录
     */
    Page<DownloadRecord> findByFileType(String fileType, Pageable pageable);
}
