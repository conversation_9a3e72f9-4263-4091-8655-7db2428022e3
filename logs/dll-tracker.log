2025-06-25 15:54:15.200 [dll-tracker] [none] [main] INFO  com.qudaiji.cloud.tracker.DllTrackerApplication - Starting DllTrackerApplication using Java 17.0.15 with PID 90859 (/Users/<USER>/Documents/jqm/common/backend/dll-tracker/target/classes started by lizhu<PERSON><PERSON> in /Users/<USER>/Documents/jqm/common/backend/dll-tracker)
2025-06-25 15:54:15.201 [dll-tracker] [none] [main] DEBUG com.qudaiji.cloud.tracker.DllTrackerApplication - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-06-25 15:54:15.201 [dll-tracker] [none] [main] INFO  com.qudaiji.cloud.tracker.DllTrackerApplication - The following 1 profile is active: "prod"
2025-06-25 15:54:15.366 [dll-tracker] [none] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 15:54:15.379 [dll-tracker] [none] [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 1 JPA repository interface.
2025-06-25 15:54:15.499 [dll-tracker] [none] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 9900 (http)
2025-06-25 15:54:15.503 [dll-tracker] [none] [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 15:54:15.503 [dll-tracker] [none] [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-06-25 15:54:15.517 [dll-tracker] [none] [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 15:54:15.517 [dll-tracker] [none] [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 305 ms
2025-06-25 15:54:15.564 [dll-tracker] [none] [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 15:54:15.575 [dll-tracker] [none] [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.18.Final
2025-06-25 15:54:15.583 [dll-tracker] [none] [main] INFO  o.hibernate.cache.internal.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-25 15:54:15.652 [dll-tracker] [none] [main] INFO  o.s.o.j.persistenceunit.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-25 15:54:15.661 [dll-tracker] [none] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-25 15:54:16.008 [dll-tracker] [none] [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@667a467f
2025-06-25 15:54:16.008 [dll-tracker] [none] [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-25 15:54:16.069 [dll-tracker] [none] [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-25 15:54:16.076 [dll-tracker] [none] [main] INFO  org.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.13
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-25 15:54:16.283 [dll-tracker] [none] [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-25 15:54:16.420 [dll-tracker] [none] [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 15:54:16.510 [dll-tracker] [none] [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 15:54:16.624 [dll-tracker] [none] [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 9900 (http) with context path '/'
2025-06-25 15:54:16.628 [dll-tracker] [none] [main] INFO  com.qudaiji.cloud.tracker.DllTrackerApplication - Started DllTrackerApplication in 1.618 seconds (process running for 1.794)
